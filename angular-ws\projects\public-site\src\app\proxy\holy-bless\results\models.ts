import type { ArticleContentCategory } from '../enums/article-content-category.enum';
import type { PublishStatus } from '../enums/publish-status.enum';
import type { MediaType } from '../enums/media-type.enum';
import type { ContentCategory } from '../enums/content-category.enum';
import type { DefaultOrderByField } from '../enums/default-order-by-field.enum';

export interface ArticleAggregateResult {
  id: number;
  title?: string;
  description?: string;
  keywords?: string;
  views: number;
  likes: number;
  deliveryDate?: string;
  articleContentCategory?: ArticleContentCategory;
  status?: PublishStatus;
  content?: string;
  memo?: string;
  creationTime?: string;
  lastModificationTime?: string;
  thumbnailFileId?: number;
  thumbnailFileName?: string;
  thumbnailRelativePathInBucket?: string;
  thumbnailMediaType?: MediaType;
  thumbnailUrl?: string;
  articleFiles: ArticleFileAggregateResult[];
}

export interface ArticleFileAggregateResult {
  id: number;
  articleId: number;
  fileId: number;
  title?: string;
  description?: string;
  isPrimary: boolean;
  fileName?: string;
  fileTitle?: string;
  relativePathInBucket?: string;
  mediaType?: MediaType;
  contentCategory?: ContentCategory;
  fileDeliveryDate?: string;
  fileViews: number;
  youtubeId?: string;
}

export interface ArticleSummaryResult {
  id: number;
  thumbnailUrl?: string;
  description?: string;
  title?: string;
  creationTime?: string;
  lastModificationTime?: string;
  deliveryDate?: string;
}

export interface CollectionSummaryRequest {
  skip: number;
  maxResultCount: number;
  sorting?: string;
  year?: number;
  month?: number;
}

export interface CollectionSummaryResult {
  id: number;
  name?: string;
  description?: string;
  contentCode?: string;
  orderByField?: DefaultOrderByField;
  totalRecords: number;
  articles: ArticleSummaryResult[];
}
