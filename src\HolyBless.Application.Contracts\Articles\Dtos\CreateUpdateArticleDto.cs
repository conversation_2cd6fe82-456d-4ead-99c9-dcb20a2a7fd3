using System;
using System.ComponentModel.DataAnnotations;
using HolyBless.Enums;

namespace HolyBless.Articles.Dtos
{
    public class CreateUpdateArticleDto
    {
        [Required]
        public DateTime DeliveryDate { get; set; }

        [Required]
        public string? LanguageCode { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = "";

        public int? ThumbnailFileId { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(200)]
        public string? Keywords { get; set; }

        public ArticleContentCategory ArticleContentCategory { get; set; }

        public PublishStatus Status { get; set; }

        public string? Content { get; set; }

        [StringLength(500)]
        public string? Memo { get; set; }
    }
}