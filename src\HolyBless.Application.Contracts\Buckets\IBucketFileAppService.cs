using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Buckets.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Buckets
{
    public interface IBucketFileAppService : IReadOnlyBucketFileAppService
    {
        Task<BucketFileDto> CreateAsync(CreateUpdateBucketFileDto input);

        Task<BucketFileDto> UpdateAsync(int id, CreateUpdateBucketFileDto input);

        Task DeleteAsync(int id);
    }
}