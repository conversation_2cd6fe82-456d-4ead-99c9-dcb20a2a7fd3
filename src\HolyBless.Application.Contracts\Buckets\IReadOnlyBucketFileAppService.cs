using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Buckets.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Buckets
{
    public interface IReadOnlyBucketFileAppService : IApplicationService
    {
        Task<BucketFileDto> GetAsync(int id);

        Task<PagedResultDto<BucketFileDto>> GetListAsync(PagedAndSortedResultRequestDto input);
    }
}