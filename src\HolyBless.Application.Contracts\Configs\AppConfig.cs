﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Enums;

namespace HolyBless.Configs
{
    public class AppConfig
    {
        public string Environment { get; set; } = EnvironmentConst.Dev.ToString();
        public bool ExposeWritableApi { get; set; } = false;
        public bool EnableBackgroundWorkers { get; set; } = false;
    }
}