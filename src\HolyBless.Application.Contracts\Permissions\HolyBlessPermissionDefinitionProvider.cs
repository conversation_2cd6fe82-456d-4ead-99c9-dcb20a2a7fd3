using HolyBless.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace HolyBless.Permissions;

public class HolyBlessPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(HolyBlessPermissions.GroupName);

        var booksPermission = myGroup.AddPermission(HolyBlessPermissions.EBooks.Default, L("Permission:Books"));
        booksPermission.AddChild(HolyBlessPermissions.EBooks.Create, L("Permission:Books.Create"));
        booksPermission.AddChild(HolyBlessPermissions.EBooks.Edit, L("Permission:Books.Edit"));
        booksPermission.AddChild(HolyBlessPermissions.EBooks.Delete, L("Permission:Books.Delete"));
        //Define your own permissions here. Example:
        var articlePermission = myGroup.AddPermission(HolyBlessPermissions.Articles.Default, L("Permission:Articles"));
        articlePermission.AddChild(HolyBlessPermissions.Articles.Create, L("Permission:Articles.Create"));
        articlePermission.AddChild(HolyBlessPermissions.Articles.Edit, L("Permission:Articles.Edit"));
        articlePermission.AddChild(HolyBlessPermissions.Articles.Delete, L("Permission:Articles.Delete"));

        var tagPermission = myGroup.AddPermission(HolyBlessPermissions.Tags.Default, L("Permission:Tags"));
        tagPermission.AddChild(HolyBlessPermissions.Tags.Create, L("Permission:Tags.Create"));
        tagPermission.AddChild(HolyBlessPermissions.Tags.Edit, L("Permission:Tags.Edit"));
        tagPermission.AddChild(HolyBlessPermissions.Tags.Delete, L("Permission:Tags.Delete"));

        var articleFilePermission = myGroup.AddPermission(HolyBlessPermissions.ArticleFiles.Default, L("Permission:ArticleFiles"));
        articleFilePermission.AddChild(HolyBlessPermissions.ArticleFiles.Create, L("Permission:ArticleFiles.Create"));
        articleFilePermission.AddChild(HolyBlessPermissions.ArticleFiles.Edit, L("Permission:ArticleFiles.Edit"));
        articleFilePermission.AddChild(HolyBlessPermissions.ArticleFiles.Delete, L("Permission:ArticleFiles.Delete"));

        var storageBucketsPermission = myGroup.AddPermission(HolyBlessPermissions.StorageBuckets.Default, L("Permission:StorageBuckets"));
        storageBucketsPermission.AddChild(HolyBlessPermissions.StorageBuckets.Create, L("Permission:StorageBuckets.Create"));
        storageBucketsPermission.AddChild(HolyBlessPermissions.StorageBuckets.Edit, L("Permission:StorageBuckets.Edit"));
        storageBucketsPermission.AddChild(HolyBlessPermissions.StorageBuckets.Delete, L("Permission:StorageBuckets.Delete"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<HolyBlessResource>(name);
    }
}