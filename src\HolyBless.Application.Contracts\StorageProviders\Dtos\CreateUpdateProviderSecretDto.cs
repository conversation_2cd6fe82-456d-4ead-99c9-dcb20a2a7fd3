using System.ComponentModel.DataAnnotations;

namespace HolyBless.StorageProviders.Dtos
{
    public class CreateUpdateProviderSecretDto
    {
        [Required]
        public string AccessId { get; set; } = default!;

        [Required]
        public string AccessSecretKey { get; set; } = default!;

        [Required]
        public string ApiEndPoint { get; set; } = default!;

        public string? Description { get; set; }
    }
}