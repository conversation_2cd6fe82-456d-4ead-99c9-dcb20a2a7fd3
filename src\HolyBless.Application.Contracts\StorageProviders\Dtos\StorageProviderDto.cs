using System;
using System.Collections.Generic;
using HolyBless.Enums;
using HolyBless.Lookups.Dtos;
using Volo.Abp.Application.Dtos;

namespace HolyBless.StorageProviders.Dtos
{
    public class StorageProviderDto : EntityDto<int>
    {
        public string ProviderName { get; set; } = default!;
        public string ProviderCode { get; set; } = ProviderCodeConstants.CloudFlare;
        public List<CountryDto> PreferCountries { get; set; } = new List<CountryDto>();
        public string? Description { get; set; }
        public string BindedDomain { get; set; } = "";
    }
}