using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace HolyBless.StorageProviders
{
    public interface IS3StorageManager : IApplicationService
    {
        /// <summary>
        /// Lists all buckets for a specific storage provider
        /// </summary>
        /// <param name="providerId">The storage provider ID</param>
        /// <returns>List of bucket names</returns>
        Task<List<string>> ListBucketsAsync(int providerId);

        /// <summary>
        /// Lists all files in a specific bucket
        /// </summary>
        /// <param name="bucketId">The bucket ID</param>
        /// <returns>List of file names</returns>
        Task<List<string>> ListFilesAsync(int bucketId);

        /// <summary>
        /// Downloads a file from a bucket
        /// </summary>
        /// <param name="bucketId">The bucket ID</param>
        /// <param name="fileNamePath">The file path within the bucket</param>
        /// <returns>File download result as byte array</returns>
        Task<byte[]> DownloadBucketFile(int bucketId, string fileNamePath);

        /// <summary>
        /// Downloads a file as a memory stream
        /// </summary>
        /// <param name="bucketId">The bucket ID</param>
        /// <param name="fileKey">The file key within the bucket</param>
        /// <returns>Memory stream containing the file data</returns>
        Task<MemoryStream> DownloadFileAsync(int bucketId, string fileKey);

        /// <summary>
        /// Uploads a file to a bucket
        /// </summary>
        /// <param name="bucketId">The bucket ID</param>
        /// <param name="subFolder">The subfolder within the bucket</param>
        /// <param name="fileName">The file name</param>
        /// <param name="fileStream">The file stream to upload</param>
        /// <returns>True if upload was successful</returns>
        Task<bool> UploadFileAsync(int bucketId, string subFolder, string fileName, Stream fileStream);

        /// <summary>
        /// Synchronizes files from CloudFlare R2 storage to the local database
        /// </summary>
        /// <returns>Number of files processed</returns>
        Task<int> SyncCloudFlareFilesAsync();
    }
}
