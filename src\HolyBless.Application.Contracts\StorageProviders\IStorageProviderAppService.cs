using HolyBless.Articles;
using HolyBless.StorageProviders.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.StorageProviders
{
    public interface IStorageProviderAppService : 
        ICrudAppService< // Defines CRUD methods
            StorageProviderDto, // Used to show storage providers
            int, // Primary key of the storage provider entity
            PagedAndSortedResultRequestDto, // Used for paging/sorting
            CreateUpdateStorageProviderDto> // Used to create/update a storage provider
    {
        string GetFileAccessUrlSync(string fileName, string relativePath, string preferCountry);
        Task<StorageProviderDto> GetProvidersByCountry(string preferCountry);
    }
}