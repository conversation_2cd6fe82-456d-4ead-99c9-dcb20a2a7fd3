﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HolyBless.VirtualFolders
{
    public class VirtualFolderTreeDto
    {
        public int Id { get; set; }
        public string FolderName { get; set; } = string.Empty;
        public List<VirtualFolderTreeDto> Children { get; set; } = new List<VirtualFolderTreeDto>();
        public List<VirtualFolderFileDto> BucketFiles { get; set; } = new List<VirtualFolderFileDto>();
    }
}