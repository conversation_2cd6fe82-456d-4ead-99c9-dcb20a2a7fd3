using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Domain.Interfaces;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Tags;
using HolyBless.Results;
using HolyBless.Tags.Dtos;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Articles
{
    public class ReadOnlyArticleAppService(
        IArticleRepository articleRepository,
        IRepository<Article, int> repository,
        IRepository<ArticleToTag> articleToTagRepository,
        IRepository<Tag, int> tagRepository,
        IRepository<TeacherArticleLink> teacherArticleLinkRepository
            ) : ApplicationService, IReadOnlyArticleAppService
    {
        protected readonly IArticleRepository articleRepository = articleRepository;
        protected readonly IRepository<Article, int> _repository = repository;
        protected readonly IRepository<ArticleToTag> _articleToTagRepository = articleToTagRepository;
        protected readonly IRepository<Tag, int> _tagRepository = tagRepository;
        protected readonly IRepository<TeacherArticleLink> _teacherArticleLinkRepository = teacherArticleLinkRepository;

        public async Task<ArticleDto> GetAsync(int id)
        {
            var queryable = await _repository.GetQueryableAsync();
            var article = await queryable
                .Include(x => x.ThumbnailBucketFile)
                .Include(x => x.ArticleToTags)
                .Include(x => x.ArticleFiles)
                    .ThenInclude(af => af.BucketFile)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), id);
            }

            return ObjectMapper.Map<Article, ArticleDto>(article);
        }

        public async Task<PagedResultDto<ArticleDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Include(x => x.ThumbnailBucketFile)
                .Include(x => x.ArticleToTags)
                // ArticleFiles are not included when retrieving a list to optimize performance
                .OrderBy(input.Sorting ?? "Title")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var articles = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<ArticleDto>(
                totalCount,
                ObjectMapper.Map<List<Article>, List<ArticleDto>>(articles)
            );
        }

        public async Task<List<TagDto>> GetTagsAsync(int articleId)
        {
            // Verify article exists
            var article = await _repository.GetAsync(articleId);
            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), articleId);
            }

            // Get article tags using a join
            var articleTagsQueryable = await _articleToTagRepository.GetQueryableAsync();
            var tagsQueryable = await _tagRepository.GetQueryableAsync();

            var query = from articleTag in articleTagsQueryable
                        join tag in tagsQueryable on articleTag.TagId equals tag.Id
                        where articleTag.ArticleId == articleId
                        select tag;

            // Include ContentCode to properly map to TagDto
            query = query.Include(t => t.ContentCode);

            var tags = await AsyncExecuter.ToListAsync(query);

            return ObjectMapper.Map<List<Tag>, List<TagDto>>(tags);
        }

        public async Task<PagedResultDto<TeacherArticleLinkDto>> GetTeacherArticleLinksAsync(int studentArticleId, int skipCount, int maxResultCount, string? sorting = null)
        {
            var queryable = await _teacherArticleLinkRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.StudentArticleId == studentArticleId)
                .OrderBy(sorting ?? nameof(TeacherArticleLink.Weight));
            var items = await AsyncExecuter.ToListAsync(
                query.Skip(skipCount).Take(maxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);
            var dtos = ObjectMapper.Map<List<TeacherArticleLink>, List<TeacherArticleLinkDto>>(items);
            return new PagedResultDto<TeacherArticleLinkDto>(totalCount, dtos);
        }

        public async Task<ArticleAggregateResult?> GetArticleAggregatesAsync(int articleId)
        {
            return await articleRepository.GetArticleAggregateAsync(articleId);
               
        }
    }
}