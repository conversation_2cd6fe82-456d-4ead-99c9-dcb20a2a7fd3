using HolyBless.Configs;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Threading;
using Volo.Abp.Uow;

namespace HolyBless.BackgroundWorkers
{
    public class StorageFileCheckWorker : AsyncPeriodicBackgroundWorkerBase, ISingletonDependency
    {
        private readonly AppConfig _appConfig;

        public StorageFileCheckWorker(
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory,
            AppConfig appConfig
            )
            : base(timer, serviceScopeFactory)
        {
            Timer.Period = 1000 * 60 * 60; //1hr
            _appConfig = appConfig;
            if (!_appConfig.EnableBackgroundWorkers)
            {
                return;
            }
            var timeUntilMidnight = GetTimeDiffToChinaMiddleNight();
            // If it's already past midnight today, start immediately, otherwise wait until midnight
            if (timeUntilMidnight.TotalMilliseconds <= 0)
            {
                Timer.Period = 1000; // Start immediately (1 second)
            }
            else
            {
                Timer.Period = (int)timeUntilMidnight.TotalMilliseconds;
            }
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            Logger.LogInformation("StorageFileCheckWorker is working...");
            if (Timer.Period != 24 * 60 * 60 * 1000) // 24 hours in milliseconds
            {
                Timer.Period = 24 * 60 * 60 * 1000;
                Logger.LogInformation("Timer period set to 24 hours for subsequent runs.");
            }

            try
            {
                // Create a scope for dependency injection
                using var scope = ServiceScopeFactory.CreateScope();
                var unitOfWorkManager = scope.ServiceProvider.GetRequiredService<IUnitOfWorkManager>();

                using var uow = unitOfWorkManager.Begin();

                // Your background work logic here
                await PerformHealthCheckAsync();

                await uow.CompleteAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "An error occurred while executing StorageFileCheckWorker.");
            }
        }

        private static async Task PerformHealthCheckAsync()
        {
            // Example: Check storage provider health
            // You can inject your application services here

            // Simulate some work
            await Task.Delay(1000);

            // Add your actual health check logic here
            // For example:
            // - Check if storage buckets are accessible
            // - Verify storage provider configurations
            // - Clean up expired data
            // - Update storage statistics
        }

        private static TimeSpan GetTimeDiffToChinaMiddleNight()
        {
            // Get the current time in China time zone
            var chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
            var now = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, chinaTimeZone);

            // Calculate the difference to the next midnight
            var nextMidnight = now.Date.AddDays(1); // Next midnight in China time
            var utcNextMidnight = TimeZoneInfo.ConvertTimeToUtc(nextMidnight, chinaTimeZone);
            var timeUntilMidnight = utcNextMidnight - DateTime.UtcNow;
            return timeUntilMidnight;
        }
    }
}