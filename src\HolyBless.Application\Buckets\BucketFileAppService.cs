using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using HolyBless.Permissions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using HolyBless.Entities.Buckets;
using System.Linq;
using System.Linq.Dynamic.Core;
using HolyBless.Configs;
using Microsoft.EntityFrameworkCore;
using HolyBless.Buckets.Dtos;
using Volo.Abp;
using static HolyBless.Permissions.HolyBlessPermissions;
using HolyBless.Helpers;
using Microsoft.AspNetCore.Mvc;

namespace HolyBless.Buckets
{
    public class BucketFileAppService : ReadOnlyBucketFileAppService, IBucketFileAppService
    {
        private readonly IRepository<BucketFile, int> _repository;

        public BucketFileAppService(
            IRepository<BucketFile, int> repository
        ) : base(repository)
        {
            _repository = repository;
        }

        [Authorize(HolyBlessPermissions.BucketFiles.Create)]
        public async Task<BucketFileDto> CreateAsync(CreateUpdateBucketFileDto input)
        {
            var bucketFile = ObjectMapper.Map<CreateUpdateBucketFileDto, BucketFile>(input);
            bucketFile.MediaType = MediaTypeHelper.GetMediaType(input.FileName);
            bucketFile = await _repository.InsertAsync(bucketFile, autoSave: true);
            return ObjectMapper.Map<BucketFile, BucketFileDto>(bucketFile);
        }

        [Authorize(HolyBlessPermissions.BucketFiles.Edit)]
        public async Task<BucketFileDto> UpdateAsync(int id, CreateUpdateBucketFileDto input)
        {
            var bucketFile = await _repository.GetAsync(id);
            ObjectMapper.Map(input, bucketFile);
            bucketFile.MediaType = MediaTypeHelper.GetMediaType(input.FileName);
            bucketFile = await _repository.UpdateAsync(bucketFile, true);
            return ObjectMapper.Map<BucketFile, BucketFileDto>(bucketFile);
        }

        [Authorize(HolyBlessPermissions.BucketFiles.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id, true);
        }
    }
}