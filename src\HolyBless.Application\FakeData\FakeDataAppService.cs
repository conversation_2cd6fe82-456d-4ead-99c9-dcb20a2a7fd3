using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Collections;
using HolyBless.Enums;
using HolyBless.FakeData.DataSeeders;
using HolyBless.FakeData.DataSeeders.HolyBless.FakeData.DataSeeders;
using HolyBless.Interfaces;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace HolyBless.FakeData
{
    [RemoteService(false)]
    public class FakeDataAppService : ApplicationService, IFakeDataAppService
    {
        private readonly IRepository<Article, int> _articleRepository;
        private readonly ICollectionRepository _collectionRepository;
        private readonly IRepository<CollectionToArticle> _collectionToArticleRepository;

        public FakeDataAppService(
            IRepository<Article, int> articleRepository,
            ICollectionRepository collectionRepository,
            IRepository<CollectionToArticle> collectionToArticleRepository)
        {
            _articleRepository = articleRepository;
            _collectionRepository = collectionRepository;
            _collectionToArticleRepository = collectionToArticleRepository;
        }

        [UnitOfWork]
        public async Task GenerateAllLanguagesDataAsync()
        {
            Logger.LogInformation("Starting to generate fake data for all languages...");

            try
            {
                await GenerateSimplifiedChineseDataAsync();
                await GenerateTraditionalChineseDataAsync();
                await GenerateEnglishDataAsync();

                Logger.LogInformation("Successfully generated fake data for all languages.");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while generating fake data for all languages.");
                throw;
            }
        }

        [UnitOfWork]
        public async Task GenerateSimplifiedChineseDataAsync()
        {
            Logger.LogInformation("Generating Simplified Chinese fake data...");

            try
            {
                var existingCollections = await _collectionRepository.GetListAsync(x => x.LanguageCode == LangCode.SimplifiedChinese);
                if (existingCollections.Any())
                {
                    await _collectionRepository.HardDeleteAsync(existingCollections);
                }
                // Generate Collections (Simplified Chinese)
                var collections = CollectionDataHansGenerator.GenerateCollections();
                await _collectionRepository.InsertManyAsync(collections, autoSave: true);

                // Generate Articles (Simplified Chinese)
                var articles = ArticleDataHansGenerator.GenerateArticles(100);
                await _articleRepository.InsertManyAsync(articles, autoSave: true);

                // Generate Collection-Article relationships
                var collectionToArticles = CollectionToArticleDataGenerator.GenerateBalancedCollectionToArticles(
                    collections, articles);
                await _collectionToArticleRepository.InsertManyAsync(collectionToArticles, autoSave: true);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while generating Simplified Chinese fake data.");
                throw;
            }
        }

        [UnitOfWork]
        public async Task GenerateTraditionalChineseDataAsync()
        {
            Logger.LogInformation("Generating Traditional Chinese fake data...");

            try
            {
                var existingCollections = await _collectionRepository.GetListAsync(x => x.LanguageCode == LangCode.TraditionalChinese);
                if (existingCollections.Any())
                {
                    await _collectionRepository.HardDeleteAsync(existingCollections);
                }
                // Generate Collections (Traditional Chinese)
                var collections = CollectionDataHantGenerator.GenerateCollections();
                await _collectionRepository.InsertManyAsync(collections, autoSave: true);

                // Generate Articles (Traditional Chinese)
                var articles = ArticleDataHantGenerator.GenerateArticles(100);
                await _articleRepository.InsertManyAsync(articles, autoSave: true);

                // Generate Collection-Article relationships
                var collectionToArticles = CollectionToArticleDataGenerator.GenerateBalancedCollectionToArticles(
                    collections, articles);
                await _collectionToArticleRepository.InsertManyAsync(collectionToArticles, autoSave: true);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while generating Traditional Chinese fake data.");
                throw;
            }
        }

        [UnitOfWork]
        public async Task GenerateEnglishDataAsync()
        {
            Logger.LogInformation("Generating English fake data...");

            try
            {
                var existingCollections = await _collectionRepository.GetListAsync(x => x.LanguageCode == LangCode.English);
                if (existingCollections.Any())
                {
                    await _collectionRepository.HardDeleteAsync(existingCollections);
                }
                // Generate Collections (English)
                var collections = CollectionDataEngGenerator.GenerateCollections();
                await _collectionRepository.InsertManyAsync(collections, autoSave: true);

                // Generate Articles (English)
                var articles = ArticleDataEngGenerator.GenerateArticles(100);
                await _articleRepository.InsertManyAsync(articles, autoSave: true);

                // Generate Collection-Article relationships
                var collectionToArticles = CollectionToArticleDataGenerator.GenerateBalancedCollectionToArticles(
                    collections, articles);
                await _collectionToArticleRepository.InsertManyAsync(collectionToArticles, autoSave: true);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred while generating English fake data.");
                throw;
            }
        }
    }
}