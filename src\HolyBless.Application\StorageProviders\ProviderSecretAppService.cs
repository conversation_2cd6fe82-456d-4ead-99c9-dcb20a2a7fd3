using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Entities.Buckets;
using HolyBless.Permissions;
using HolyBless.StorageProviders.Dtos;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.StorageProviders
{
    [Authorize(HolyBlessPermissions.ProviderSecrets.Default)]
    [RemoteService(false)]
    public class ProviderSecretAppService : HolyBlessAppService, IProviderSecretAppService
    {
        private readonly IRepository<ProviderSecret, int> _repository;

        public ProviderSecretAppService(
            IRepository<ProviderSecret, int> repository)
        {
            _repository = repository;
        }

        public async Task<ProviderSecretDto> GetAsync(int id)
        {
            var secret = await _repository.GetAsync(id);
            return ObjectMapper.Map<ProviderSecret, ProviderSecretDto>(secret);
        }

        public async Task<PagedResultDto<ProviderSecretDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .OrderBy(input.Sorting ?? "Id")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var secrets = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<ProviderSecretDto>(
                totalCount,
                ObjectMapper.Map<List<ProviderSecret>, List<ProviderSecretDto>>(secrets)
            );
        }

        [Authorize(HolyBlessPermissions.ProviderSecrets.Create)]
        public async Task<ProviderSecretDto> CreateAsync(CreateUpdateProviderSecretDto input)
        {
            var secret = ObjectMapper.Map<CreateUpdateProviderSecretDto, ProviderSecret>(input);
            secret = await _repository.InsertAsync(secret, autoSave: true);

            return await GetAsync(secret.Id);
        }

        [Authorize(HolyBlessPermissions.ProviderSecrets.Edit)]
        public async Task<ProviderSecretDto> UpdateAsync(int id, CreateUpdateProviderSecretDto input)
        {
            var secret = await _repository.GetAsync(id);
            ObjectMapper.Map(input, secret);
            secret = await _repository.UpdateAsync(secret, autoSave: true);

            return await GetAsync(secret.Id);
        }

        [Authorize(HolyBlessPermissions.ProviderSecrets.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }
    }
}