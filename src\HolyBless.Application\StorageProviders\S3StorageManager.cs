﻿using System;
using System.Collections.Generic;
using System.IO;
using Microsoft.Extensions.Options;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using Amazon.S3;
using Amazon.S3.Model;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using HolyBless.Configs;
using HolyBless.Enums;
using HolyBless.Exceptions;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp;
using HolyBless.Entities.Buckets;
using HolyBless.StorageProviders;
using HolyBless.Helpers;

namespace HolyBless.StorageProviders
{
    [RemoteService(false)]
    public class S3StorageManager : ApplicationService, IS3StorageManager
    {
        private readonly IRepository<StorageProvider> _providerRepository;
        private readonly IRepository<StorageBucket> _bucketRepository;
        private readonly IRepository<ProviderSecret> _providerSecretRepository;
        private readonly IRepository<BucketFile> _bucketFileRepository;
        private readonly IStorageProviderAppService _storageProviderAppService;
        private readonly AppConfig _settings;

        public S3StorageManager
            (
              IRepository<StorageProvider> providerRepository
            , IRepository<StorageBucket> bucketRepository
            , IRepository<ProviderSecret> providerSecretRepository
            , IRepository<BucketFile> bucketFileRepository
            , IStorageProviderAppService storageProviderAppService
            , IOptions<AppConfig> settings
            )
        {
            _providerRepository = providerRepository;
            _bucketRepository = bucketRepository;
            _providerSecretRepository = providerSecretRepository;
            _bucketFileRepository = bucketFileRepository;
            _storageProviderAppService = storageProviderAppService;
            _settings = settings.Value;
        }

        [HttpGet]
        public async Task<List<string>> ListBucketsAsync(int providerId)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            var provider = await _providerRepository.FindAsync(p => p.Id == providerId);
            Check.NotNull(provider, nameof(StorageProvider));

            // These properties are no longer available in StorageProvider class
            //return await ListBucketsAsync(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
            throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
        }

        [HttpGet]
        public async Task<List<string>> ListBucketsAsync(string accessId, string accessSecretKey, string apiEndPoint)
        {
            // NOTE: This method is still functional but can't be called from ListBucketsAsync(int providerId) anymore
            var bucketNames = new List<string>();
            try
            {
                var s3Client = GetS3Client(accessId, accessSecretKey, apiEndPoint);
                var response = await s3Client.ListBucketsAsync();
                foreach (var bucket in response.Buckets)
                {
                    bucketNames.Add(bucket.BucketName);
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }

            return bucketNames;
        }

        private async Task<StorageBucket> GetBucketById(int bucketId)
        {
            var query = await _bucketRepository.GetQueryableAsync();
            var bucket = await query.Include(x => x.StorageProvider).FirstOrDefaultAsync(x => x.Id == bucketId);
            Check.NotNull(bucket, nameof(StorageBucket));
            return bucket;
        }

        private AmazonS3Client GetS3Client(string accessId, string accessSecretKey, string apiEndPoint)
        {
            var config = new AmazonS3Config
            {
                ServiceURL = apiEndPoint,
                ForcePathStyle = false // AliYun requires path-style addressing to false
            };

            if (apiEndPoint.Contains(".r2.", StringComparison.OrdinalIgnoreCase))
            {
                config.ForcePathStyle = true; //R2 requires path-style addressing to true
            }
            var s3Client = new AmazonS3Client(accessId, accessSecretKey, config);
            return s3Client;
        }

        [HttpGet]
        public async Task<List<string>> ListSubfoldersAsync(int bucketId, string prefix = "")
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            var subfolders = new List<string>();

            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                var request = new ListObjectsV2Request
                {
                    BucketName = bucket.BucketName,
                    Prefix = prefix,         // Start from a given prefix
                    Delimiter = "/"          // Treat `/` as a folder separator
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // var response = await s3Client.ListObjectsV2Async(request);

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");

                // Fetch common prefixes (subfolders)
                // subfolders.AddRange(response.CommonPrefixes);

                // Optionally, print objects (files) at the current level
                // foreach (var obj in response.S3Objects)
                // {
                //     Console.WriteLine($"File: {obj.Key}");
                // }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }

            return subfolders;
        }

        [HttpGet]
        public async Task<List<string>> ListFilesAsync(int bucketId)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
        }

        [HttpGet]
        [Route("DownloadBucketFile")]
        public async Task<byte[]> DownloadBucketFile(int bucketId, string fileNamePath)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            // MemoryStream memory = await DownloadFileAsync(bucketId, fileNamePath);
            // memory.Position = 0;

            // var contentType = "application/octet-stream"; // Change based on file type if needed
            // var fileName = Path.GetFileName(fileNamePath);
            // return new FileStreamResult(memory, contentType)
            // {
            //     FileDownloadName = fileName
            // };

            throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
        }

        public async Task<bool> UploadFileAsync(int bucketId, string subFolder, string fileName, Stream fileStream)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                // Combine subfolder and filename to create the object key
                var objectKey = $"{subFolder.TrimEnd('/')}/{Path.GetFileName(fileName)}";

                var request = new PutObjectRequest
                {
                    BucketName = bucket.BucketName,
                    Key = objectKey,
                    InputStream = fileStream,
                    ContentType = "application/octet-stream" // Set appropriate MIME type if known
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // var response = await s3Client.PutObjectAsync(request);

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");

                // if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                // {
                //     return true;
                // }
                // else
                // {
                //     Logger.LogError("Failed to upload file. StatusCode: {StatusCode}", response.HttpStatusCode);
                //     return false;
                // }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<MemoryStream> DownloadFileAsync(int bucketId, string fieKey)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                var request = new GetObjectRequest
                {
                    BucketName = bucket.BucketName,
                    Key = fieKey
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // using (var response = await s3Client.GetObjectAsync(request))
                // {
                //     var memoryStream = new MemoryStream();
                //     await response.ResponseStream.CopyToAsync(memoryStream);
                //     return memoryStream;
                // }

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        /// <summary>
        /// Walks through all CloudFlare storage buckets and their files, updating the BucketFile table
        /// </summary>
        /// <returns>Number of files processed</returns>
        [HttpPost]
        public async Task<int> SyncCloudFlareFilesAsync()
        {
            var filesProcessed = 0;

            try
            {
                // Get all distinct StorageBuckets for CloudFlare providers matching current environment
                var cfBuckets = await GetCloudFlareBucketsAsync();

                if (!cfBuckets.Any())
                {
                    Logger.LogWarning("No CloudFlare storage buckets found for environment: {Environment}", _settings.Environment);
                    return 0;
                }

                // Get provider secret (assuming there's one secret for CloudFlare)
                var providerSecret = await GetProviderSecretAsync();

                // Process each bucket
                foreach (var bucket in cfBuckets)
                {
                    Logger.LogInformation("Processing bucket: {BucketName} for provider: {ProviderName}",
                        bucket.BucketName, bucket.StorageProvider.ProviderName);

                    var bucketFilesProcessed = await ProcessBucketFilesAsync(bucket, providerSecret);
                    filesProcessed += bucketFilesProcessed;

                    Logger.LogInformation("Processed {FileCount} files in bucket: {BucketName}",
                        bucketFilesProcessed, bucket.BucketName);
                }

                Logger.LogInformation("Total files processed: {TotalFiles}", filesProcessed);
                return filesProcessed;
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        private async Task<List<StorageBucket>> GetCloudFlareBucketsAsync()
        {
            // Use the cached bucket DTOs from StorageProviderAppService and convert to entities
            var bucketDtos = await _storageProviderAppService.GetCloudFlareBucketsAsync();

            // Convert DTOs back to entities by fetching from repository
            var bucketIds = bucketDtos.Select(dto => dto.Id).ToList();
            var query = await _bucketRepository.GetQueryableAsync();
            return await query
                .Include(x => x.StorageProvider)
                .Where(x => bucketIds.Contains(x.Id))
                .ToListAsync();
        }

        private async Task<ProviderSecret> GetProviderSecretAsync()
        {
            // For now, get the first available provider secret
            // In a real scenario, you might want to link secrets to specific providers
            var secret = await _providerSecretRepository.FirstOrDefaultAsync();
            Check.NotNull(secret, nameof(ProviderSecret), "No provider secret found. Please configure CloudFlare credentials.");
            return secret;
        }

        private async Task<int> ProcessBucketFilesAsync(StorageBucket bucket, ProviderSecret secret)
        {
            var filesProcessed = 0;

            try
            {
                var s3Client = GetS3Client(secret.AccessId, secret.AccessSecretKey, secret.ApiEndPoint);

                // List all objects in the bucket
                var request = new ListObjectsV2Request
                {
                    BucketName = bucket.BucketName,
                    MaxKeys = 1000 // Process in batches
                };

                ListObjectsV2Response response;
                do
                {
                    response = await s3Client.ListObjectsV2Async(request);

                    foreach (var s3Object in response.S3Objects)
                    {
                        // Skip folders (objects ending with /)
                        if (s3Object.Key.EndsWith("/"))
                            continue;

                        await ProcessSingleFileAsync(bucket, s3Object);
                        filesProcessed++;
                    }

                    // Set continuation token for next batch
                    request.ContinuationToken = response.NextContinuationToken;

                } while (response.IsTruncated);

                return filesProcessed;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing files for bucket: {BucketName}", bucket.BucketName);
                throw;
            }
        }

        private async Task ProcessSingleFileAsync(StorageBucket bucket, S3Object s3Object)
        {
            try
            {
                // Extract file name from the key (path)
                var fileName = Path.GetFileName(s3Object.Key);
                var relativePath = Path.GetDirectoryName(s3Object.Key)?.Replace("\\", "/") ?? "";

                // Check if file already exists in database
                var existingFile = await _bucketFileRepository.FirstOrDefaultAsync(f =>
                    f.FileName == fileName &&
                    f.RelativePathInBucket == relativePath &&
                    f.Environment == bucket.StorageProvider.Environment);

                if (existingFile != null)
                {
                    // Update existing file
                    existingFile.Exists = true;
                    existingFile.LastModificationTime = s3Object.LastModified;
                    await _bucketFileRepository.UpdateAsync(existingFile);
                }
                else
                {
                    // Create new file record
                    var bucketFile = new BucketFile
                    {
                        FileName = fileName,
                        RelativePathInBucket = relativePath,
                        LanguageCode = bucket.LanguageCode,
                        SpokenLangCode = bucket.SpokenLangCode,
                        Environment = bucket.StorageProvider.Environment,
                        Exists = true,
                        MediaType = MediaTypeHelper.GetMediaType(fileName),
                        ContentCategory = bucket.ContentType,
                        DeliveryDate = DateTime.UtcNow,
                        Views = 0
                    };

                    // Set LastModificationTime from S3 object
                    bucketFile.LastModificationTime = s3Object.LastModified;

                    await _bucketFileRepository.InsertAsync(bucketFile);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing file: {FileName} in bucket: {BucketName}",
                    s3Object.Key, bucket.BucketName);
                // Continue processing other files
            }
        }


    }
}