using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Configs;
using HolyBless.Enums;
using HolyBless.Exceptions;
using HolyBless.Lookups;
using HolyBless.Permissions;
using HolyBless.StorageProviders.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.StorageProviders
{
    [Authorize(HolyBlessPermissions.StorageProviders.Default)]
    [RemoteService(false)]
    public class StorageProviderAppService : HolyBlessAppService, IStorageProviderAppService
    {
        protected readonly IRepository<StorageProvider, int> _repository;
        protected readonly IRepository<Country, int> _countryRepository;
        protected readonly IMemoryCache _memoryCache;
        protected readonly AppConfig _settings;
        protected static readonly string KeyProvider = "provider_cache";

        protected readonly MemoryCacheEntryOptions _memoryCacheEntryOptions = new()
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(600),
            SlidingExpiration = TimeSpan.FromMinutes(60),
            Priority = CacheItemPriority.High
        };

        public StorageProviderAppService(
            IRepository<StorageProvider, int> repository
            , IRepository<Country, int> countryRepository
            , IMemoryCache memoryCache
            , AppConfig settings
            )
        {
            _memoryCache = memoryCache;
            _settings = settings;
            _repository = repository;
            _countryRepository = countryRepository;
        }

        public async Task<StorageProviderDto> GetAsync(int id)
        {
            var storageProvider = await _repository.GetAsync(id, includeDetails: true);
            return ObjectMapper.Map<StorageProvider, StorageProviderDto>(storageProvider);
        }

        public async Task<PagedResultDto<StorageProviderDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var env = _settings.Environment;
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.Environment == env)
                .OrderBy(input.Sorting ?? "ProviderName")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var storageProviders = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<StorageProviderDto>(
                totalCount,
                ObjectMapper.Map<List<StorageProvider>, List<StorageProviderDto>>(storageProviders)
            );
        }

        [Authorize(HolyBlessPermissions.StorageProviders.Create)]
        public async Task<StorageProviderDto> CreateAsync(CreateUpdateStorageProviderDto input)
        {
            var storageProvider = ObjectMapper.Map<CreateUpdateStorageProviderDto, StorageProvider>(input);
            storageProvider.Environment = _settings.Environment;

            // Set the preferred countries
            if (input.PreferCountryIds.Any())
            {
                var countries = await _countryRepository.GetListAsync(c => input.PreferCountryIds.Contains(c.Id));
                storageProvider.PreferCountries = countries;
            }

            storageProvider = await _repository.InsertAsync(storageProvider, autoSave: true);
            return ObjectMapper.Map<StorageProvider, StorageProviderDto>(storageProvider);
        }

        [Authorize(HolyBlessPermissions.StorageProviders.Edit)]
        public async Task<StorageProviderDto> UpdateAsync(int id, CreateUpdateStorageProviderDto input)
        {
            var storageProvider = await _repository.GetAsync(id, includeDetails: true);
            ObjectMapper.Map(input, storageProvider);

            // Update the preferred countries
            storageProvider.PreferCountries.Clear();
            if (input.PreferCountryIds.Any())
            {
                var countries = await _countryRepository.GetListAsync(c => input.PreferCountryIds.Contains(c.Id));
                foreach (var country in countries)
                {
                    storageProvider.PreferCountries.Add(country);
                }
            }

            storageProvider = await _repository.UpdateAsync(storageProvider, true);
            return ObjectMapper.Map<StorageProvider, StorageProviderDto>(storageProvider);
        }

        [Authorize(HolyBlessPermissions.StorageProviders.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id, true);
        }

        protected async Task<List<StorageProvider>> GetCachedAllProviders()
        {
            if (!_memoryCache.TryGetValue(KeyProvider, out List<StorageProvider>? providers))
            {
                var query = await _repository.GetQueryableAsync();
                providers = await query.Include(x => x.PreferCountries).Include(x => x.Buckets)
                    .ToListAsync();
                if (providers == null || providers.Count == 0)
                {
                    throw new Volo.Abp.BusinessException("Could not find any storage provider");
                }
                // Cache the result
                _memoryCache.Set(KeyProvider, providers, _memoryCacheEntryOptions);
            }
            return providers ?? [];
        }

        public async Task<StorageProviderDto> GetProvidersByCountry(string preferCountry)
        {
            var allProviders = await GetCachedAllProviders();
            var provider = allProviders.FirstOrDefault(p =>
                p.PreferCountries.Any(c => c.Code == preferCountry) &&
                p.Environment == _settings.Environment);

            //If could not find prefered country provider, use China's
            if (provider == null)
            {
                if (preferCountry == CountryCodeConstants.HongKong || preferCountry == CountryCodeConstants.Macao)
                {
                    provider = allProviders.FirstOrDefault(p =>
                        p.PreferCountries.Any(c => c.Code == CountryCodeConstants.China) &&
                        p.Environment == _settings.Environment);
                }
                else
                {
                    provider = allProviders.FirstOrDefault(p =>
                        p.PreferCountries.Any(c => c.Code == CountryCodeConstants.UnitedStates) &&
                        p.Environment == _settings.Environment);
                }
            }
            if (provider == null)
            {
                throw new Volo.Abp.BusinessException("Could not find any storage provider");
            }
            return ObjectMapper.Map<StorageProvider, StorageProviderDto>(provider);
        }

        public string GetFileAccessUrlSync(string fileName, string relativePath, string preferCountry)
        {
            Check.NotNullOrWhiteSpace(fileName, nameof(fileName));

            var cacheKey = $"{KeyProvider}_{fileName}_{relativePath}_{preferCountry}";
            if (!_memoryCache.TryGetValue(cacheKey, out string? fileAccessUrl))
            {
                // Simulate fetching the URL from a storage provider
                fileAccessUrl = $"https://{preferCountry}.storageprovider.com/{relativePath}/{fileName}";
                // Cache the result
                _memoryCache.Set(cacheKey, fileAccessUrl, _memoryCacheEntryOptions);
            }
            return fileAccessUrl ?? string.Empty;
        }

        public async Task<List<StorageBucketDto>> GetCloudFlareBucketsAsync()
        {
            var allProviders = await GetCachedAllProviders();
            var cfBuckets = allProviders
                .Where(p => p.ProviderCode == ProviderCodeConstants.CloudFlare &&
                           p.Environment == _settings.Environment)
                .SelectMany(p => p.Buckets)
                .ToList();

            return ObjectMapper.Map<List<StorageBucket>, List<StorageBucketDto>>(cfBuckets);
        }
    }
}