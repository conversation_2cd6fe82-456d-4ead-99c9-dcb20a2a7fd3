using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Entities.VirtualFolders;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.VirtualFolders
{
    [RemoteService(Name = "VirtualFolderAppService")]
    public class ReadOnlyVirtualFolderAppService : ApplicationService, IReadOnlyVirtualFolderAppService
    {
        protected readonly IRepository<VirtualDiskFolder, int> _virtualFolderRepository;
        protected readonly IRepository<FolderToBucketFile> _folderToBucketFileRepository;
        protected readonly IRepository<VirtualDiskFolderTree, int> _folderTreeRepository;

        public ReadOnlyVirtualFolderAppService(
             IRepository<VirtualDiskFolder, int> virtualFolderRepository,
             IRepository<FolderToBucketFile> folderToBucketFileRepository,
             IRepository<VirtualDiskFolderTree, int> folderTreeRepository)
        {
            _folderToBucketFileRepository = folderToBucketFileRepository;
            _folderTreeRepository = folderTreeRepository;
            _virtualFolderRepository = virtualFolderRepository;
        }

        public async Task<string> GetFolderTreeJson(int rootFolderId)
        {
            var tree = await _folderTreeRepository.GetAsync(rootFolderId);
            return tree.TreeJsonData;
        }

        public async Task<VirtualFolderTreeDto> GetFolderTreeWithFilesAsync(int rootFolderId)
        {
            var rootFolder = await _virtualFolderRepository.GetAsync(rootFolderId);
            if (rootFolder == null)
            {
                throw new EntityNotFoundException(typeof(VirtualDiskFolder), rootFolderId);
            }
            var allFolders = await _virtualFolderRepository.GetListAsync();
            var allFolderToFileMappings = await _folderToBucketFileRepository.GetListAsync();
            return BuildTree(rootFolder, allFolders, allFolderToFileMappings);
        }

        private VirtualFolderTreeDto BuildTree(
            VirtualDiskFolder folder,
            List<VirtualDiskFolder> allFolders,
            List<FolderToBucketFile> allFolderToFileMappings)
        {
            var folderDto = new VirtualFolderTreeDto
            {
                Id = folder.Id,
                FolderName = folder.FolderName
            };
            folderDto.BucketFiles = allFolderToFileMappings
                .Where(mapping => mapping.FolderId == folder.Id)
                .Select(mapping => new VirtualFolderFileDto
                {
                    Id = mapping.BucketFileId,
                    FileName = mapping.BucketFile.FileName,
                    Title = mapping.BucketFile.Title
                })
                .ToList();
            var childFolders = allFolders
                .Where(f => f.ParentFolderId == folder.Id)
                .ToList();
            folderDto.Children = childFolders
                .Select(child => BuildTree(child, allFolders, allFolderToFileMappings))
                .ToList();
            return folderDto;
        }
    }
}