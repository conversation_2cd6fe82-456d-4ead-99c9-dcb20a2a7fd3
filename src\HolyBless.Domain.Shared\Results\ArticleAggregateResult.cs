using System;
using System.Collections.Generic;
using HolyBless.Enums;

namespace HolyBless.Results
{
    public class ArticleAggregateResult
    {
        // Article properties
        public int Id { get; set; }
        public string Title { get; set; } = default!;
        public string? Description { get; set; }
        public string? Keywords { get; set; }
        public int Views { get; set; }
        public int Likes { get; set; }
        public DateTime DeliveryDate { get; set; }
        public ArticleContentCategory ArticleContentCategory { get; set; }
        public PublishStatus Status { get; set; }
        public string? Content { get; set; }
        public string? Memo { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }

        // Thumbnail file info
        public int? ThumbnailFileId { get; set; }
        public string? ThumbnailFileName { get; set; }
        public string? ThumbnailRelativePathInBucket { get; set; }
        public MediaType? ThumbnailMediaType { get; set; }
        public string? ThumbnailUrl { get; set; }

        // Article files
        public List<ArticleFileAggregateResult> ArticleFiles { get; set; } = [];
    }

    public class ArticleFileAggregateResult
    {
        // ArticleFile properties
        public int Id { get; set; }
        public int ArticleId { get; set; }
        public int FileId { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public bool IsPrimary { get; set; }

        // BucketFile properties
        public string FileName { get; set; } = default!;
        public string? FileTitle { get; set; }
        public string RelativePathInBucket { get; set; } = default!;
        public MediaType MediaType { get; set; }
        public ContentCategory ContentCategory { get; set; }
        public DateTime FileDeliveryDate { get; set; }
        public int FileViews { get; set; }
        public string? YoutubeId { get; set; }
    }
}
