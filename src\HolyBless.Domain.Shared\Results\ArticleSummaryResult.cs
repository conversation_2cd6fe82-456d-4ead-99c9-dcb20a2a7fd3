﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HolyBless.Results
{
    //For better performance, has to put some Dtos in Domain project
    public class ArticleSummaryResult
    {
        public int Id { get; set; }
        public string? ThumbnailUrl { get; set; } //Calculated URL of the thumbnail image
        public string Description { get; set; } = default!; //Description of the article, when show in list page
        public string Title { get; set; } = default!; //Title of the article
        public DateTime CreationTime { get; set; }
        public DateTime LastModificationTime { get; set; }
        public DateTime DeliveryDate { get; set; } //Delivery date of the article, used to indicate the same article across different languages
    }
}