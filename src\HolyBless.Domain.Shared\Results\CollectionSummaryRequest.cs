﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Enums;

namespace HolyBless.Results
{
    public class CollectionSummaryRequest
    {
        public int Skip { get; set; }
        public int MaxResultCount { get; set; } = 20;
        public string Sorting { get; set; } = "CreationTime desc";
        public int? Year { get; set; }
        public int? Month { get; set; }
    }
}