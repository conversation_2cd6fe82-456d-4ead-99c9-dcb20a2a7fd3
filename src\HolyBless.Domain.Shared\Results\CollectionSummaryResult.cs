﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Enums;

namespace HolyBless.Results
{
    public class CollectionSummaryResult
    {
        public int Id { get; set; }
        public string Name { get; set; } = default!;
        public string? Description { get; set; }
        public string ContentCode { get; set; } = default!;
        public DefaultOrderByField OrderByField { get; set; } = DefaultOrderByField.CreationTime;
        public int TotalRecords { get; set; }
        public List<ArticleSummaryResult> Articles { get; set; } = [];
    }
}