﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Enums;
using HolyBless.Lookups;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities
{
    public class LanguageAuditedAggregateRoot<TKey> : FullAuditedAggregateRoot<TKey>
    {
        public virtual string? LanguageCode { get; set; }

        protected LanguageAuditedAggregateRoot()
        {
        }

        protected LanguageAuditedAggregateRoot(TKey id)
        : base(id)
        {
        }
    }
}