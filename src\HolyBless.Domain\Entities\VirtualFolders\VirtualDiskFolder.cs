﻿using System.Collections;
using System.Collections.Generic;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Channels;
using HolyBless.Enums;
using HolyBless.Lookups;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.VirtualFolders
{
    public class VirtualDiskFolder : FullAuditedAggregateRoot<int>
    {
        public int? ParentFolderId { get; set; }
        public VirtualDiskFolder? ParentFolder { get; set; }
        public string LanguageCode { get; set; } = LangCode.SimplifiedChinese;
        public string FolderName { get; set; } = default!;

        public int Views { get; set; } = 0;     //Visit times

        public int? ChannelId { get; set; }     //Under which channel
        public Channel? Channel { get; set; }
        public int? Size { get; set; }          //Size of the folder
        public int? ArticleId { get; set; }
        public Article? Article { get; set; }
        public ICollection<VirtualDiskFolder>? Children { get; set; }
        public ICollection<FolderToBucketFile> FolderToBucketFiles { get; set; } = new List<FolderToBucketFile>();
    }
}