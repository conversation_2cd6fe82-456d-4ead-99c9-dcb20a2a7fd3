﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Entities.Collections;
using HolyBless.Results;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Interfaces
{
    public interface ICollectionRepository : IRepository<Collection, int>
    {
        Task<CollectionSummaryResult> GetCollectionSummaryAsync(int collectionId, CollectionSummaryRequest request);
    }
}