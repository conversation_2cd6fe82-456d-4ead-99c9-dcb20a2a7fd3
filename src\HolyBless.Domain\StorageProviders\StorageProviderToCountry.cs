﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Enums;
using HolyBless.Lookups;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.StorageProviders
{
    public class StorageProviderToCountry : Entity, ISoftDelete
    {
        public int StorageProviderId { get; set; }
        public StorageProvider StorageProvider { get; set; } = default!;
        public int CountryId { get; set; }
        public Country Country { get; set; } = default!;
        public bool IsDeleted { get; set; }

        public override object?[] GetKeys()
        {
            return [StorageProviderId, CountryId];
        }

        public StorageProviderToCountry()
        {
        }

        public StorageProviderToCountry(int storageProviderId, int countryId)
        {
            StorageProviderId = storageProviderId;
            CountryId = countryId;
        }
    }
}