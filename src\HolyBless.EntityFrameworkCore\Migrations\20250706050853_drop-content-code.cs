﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class dropcontentcode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_VirtualDiskFolders_ContentCodes_ContentCodeId",
                table: "VirtualDiskFolders");

            migrationBuilder.DropTable(
                name: "ContentCodes");

            migrationBuilder.DropTable(
                name: "StagingFolders");

            migrationBuilder.DropIndex(
                name: "IX_VirtualDiskFolders_ContentCodeId",
                table: "VirtualDiskFolders");

            migrationBuilder.DropColumn(
                name: "ContentCodeId",
                table: "VirtualDiskFolders");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ContentCodeId",
                table: "VirtualDiskFolders",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "ContentCodes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Code = table.Column<string>(type: "character varying(50)", unicode: false, maxLength: 50, nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: false),
                    ConentCodeCategory = table.Column<int>(type: "integer", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uuid", nullable: true),
                    Description = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    ExtraProperties = table.Column<string>(type: "text", nullable: false),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContentCodes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "StagingFolders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ParentFolderId = table.Column<int>(type: "integer", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ExtraProperties = table.Column<string>(type: "text", nullable: false),
                    FolderCode = table.Column<string>(type: "character varying(50)", unicode: false, maxLength: 50, nullable: false),
                    FolderName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    LanguageCode = table.Column<string>(type: "character varying(10)", unicode: false, maxLength: 10, nullable: false),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StagingFolders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StagingFolders_StagingFolders_ParentFolderId",
                        column: x => x.ParentFolderId,
                        principalTable: "StagingFolders",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_VirtualDiskFolders_ContentCodeId",
                table: "VirtualDiskFolders",
                column: "ContentCodeId");

            migrationBuilder.CreateIndex(
                name: "IX_StagingFolders_ParentFolderId",
                table: "StagingFolders",
                column: "ParentFolderId");

            migrationBuilder.AddForeignKey(
                name: "FK_VirtualDiskFolders_ContentCodes_ContentCodeId",
                table: "VirtualDiskFolders",
                column: "ContentCodeId",
                principalTable: "ContentCodes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
