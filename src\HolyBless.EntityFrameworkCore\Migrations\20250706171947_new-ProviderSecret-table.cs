﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class newProviderSecrettable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StorageBucketToFiles");

            migrationBuilder.AlterColumn<string>(
                name: "Environment",
                table: "StorageProviders",
                type: "character varying(20)",
                unicode: false,
                maxLength: 20,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldUnicode: false,
                oldMaxLength: 20,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ComputeUrl",
                table: "BucketFiles",
                type: "character varying(256)",
                unicode: false,
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Environment",
                table: "BucketFiles",
                type: "character varying(20)",
                unicode: false,
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "Exists",
                table: "BucketFiles",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "ProviderSecrets",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    AccessId = table.Column<string>(type: "character varying(256)", unicode: false, maxLength: 256, nullable: false),
                    AccessSecretKey = table.Column<string>(type: "character varying(256)", unicode: false, maxLength: 256, nullable: false),
                    ApiEndPoint = table.Column<string>(type: "character varying(256)", unicode: false, maxLength: 256, nullable: false),
                    Description = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ExtraProperties = table.Column<string>(type: "text", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProviderSecrets", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ProviderSecrets");

            migrationBuilder.DropColumn(
                name: "ComputeUrl",
                table: "BucketFiles");

            migrationBuilder.DropColumn(
                name: "Environment",
                table: "BucketFiles");

            migrationBuilder.DropColumn(
                name: "Exists",
                table: "BucketFiles");

            migrationBuilder.AlterColumn<string>(
                name: "Environment",
                table: "StorageProviders",
                type: "character varying(20)",
                unicode: false,
                maxLength: 20,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldUnicode: false,
                oldMaxLength: 20);

            migrationBuilder.CreateTable(
                name: "StorageBucketToFiles",
                columns: table => new
                {
                    BucketId = table.Column<int>(type: "integer", nullable: false),
                    FileId = table.Column<int>(type: "integer", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StorageBucketToFiles", x => new { x.BucketId, x.FileId });
                    table.ForeignKey(
                        name: "FK_StorageBucketToFiles_BucketFiles_FileId",
                        column: x => x.FileId,
                        principalTable: "BucketFiles",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StorageBucketToFiles_StorageBuckets_BucketId",
                        column: x => x.BucketId,
                        principalTable: "StorageBuckets",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_StorageBucketToFiles_FileId",
                table: "StorageBucketToFiles",
                column: "FileId");
        }
    }
}
