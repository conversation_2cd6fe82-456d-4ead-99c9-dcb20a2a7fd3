using Bogus;
using global::HolyBless.Entities.Articles;
using HolyBless.Enums;

namespace HolyBless.FakeData.DataSeeders
{
    namespace HolyBless.FakeData.DataSeeders
    {
        public static class ArticleDataEngGenerator
        {
            private static readonly string[] EnglishArticleTitles =
            [
                "Journey of Spiritual Awakening", "Light of Wisdom Illuminates Life", "Insights on the Path of Practice", "The Power of Love and Compassion",
                "The True Meaning and Purpose of Life", "Revelations in Dreams", "Miracles of Heaven's Gate Opening", "The Power of Peace and Blessings",
                "Secrets of Spiritual Growth", "Answering Questions on Practice", "Philosophical Thoughts on Life and Death", "Life Insights from Daily Reflections",
                "Stories of Turning Back to Shore", "Mountain-like Grace of Teachers", "Sharing Practice Experiences", "Wisdom of Learning from the Past",
                "The Power of Sincere Repentance", "Records of Divine Miracles", "Random Thoughts on Life", "Drops of Life Wisdom",
                "The Path of Soul Purification", "Sharing Practice Experiences", "Moments of Life Awakening", "Transmitting the Warmth of Love",
                "Guide to a Wise Life", "The Way to Inner Peace", "Resolving Practice Confusions", "Philosophical Thoughts on Living",
                "The Power of Dreams Coming True", "Witnessing God's Abundant Grace", "Compassionate Teachings of the Master", "The Path of Repentance and Rebirth",
                "Miraculous Life Stories", "Love that Warms Hearts", "Experiences of Soul Elevation", "Sharing Practice Wisdom",
                "Exploring Life's Meaning", "Adventures in Dreamland", "Guidance on the Path of Divine Revelation", "Witnessing Peace and Protection",
                "Insights from Spiritual Awakening", "Guidance on Practice Methods", "Summary of Life Wisdom", "Records of Life Reflections",
                "The Prodigal Son Returns", "Inheriting Teacher's Wisdom", "Harvest from Practice", "Historical Revelations",
                "Confessions from the Heart", "Witnessing Divine Miracles and Grace", "Random Thoughts on Life", "Recording Life's Moments"
            ];

            private static readonly string[] EnglishDescriptions =
            [
                "Sharing experiences of spiritual growth and exploring the wisdom and power within.",
                "Recording insights and experiences on the path of practice, providing guidance for practitioners.",
                "Warming hearts with love and compassion, transmitting positive energy and hope.",
                "Exploring the true meaning of life and sharing wisdom and insights about existence.",
                "Recording adventures and revelations in dreams, analyzing their deeper meanings.",
                "Witnessing the miracles of heaven's gate opening and feeling divine power and protection.",
                "Sharing the power of peace and blessings, transmitting testimonies of God's abundant grace.",
                "Guiding methods for spiritual growth and helping with soul purification and elevation.",
                "Answering doubts on the path of practice and pointing the way for practitioners.",
                "Contemplating the mysteries of life and death, exploring philosophy and wisdom of existence.",
                "Recording daily life insights and sharing wisdom from reflections.",
                "Sharing stories of turning back to shore and witnessing the power of new beginnings.",
                "Inheriting the mountain-like grace of teachers and remembering their compassion and wisdom.",
                "Sharing experiences and insights on the path of practice, recording every detail of spiritual journey.",
                "Learning from the past to gain new knowledge, drawing wisdom and inspiration from history.",
                "Sincere repentance cleanses the soul, sharing insights and experiences of confession.",
                "Recording testimonies of divine manifestations and sharing stories and insights of miracles.",
                "Recording life insights in essays and sharing wisdom and thoughts about living.",
                "Recording life wisdom drop by drop, transmitting warmth and positive energy.",
                "Guiding the path of soul purification and helping with spiritual awakening and growth."
            ];

            private static readonly string[] EnglishKeywords =
            [
                "soul,awakening,wisdom", "practice,insight,experience", "love,compassion,warmth", "life,truth,meaning",
                "dreams,revelation,adventure", "heaven,miracle,divine", "peace,blessing,protection", "soul,growth,purification",
                "practice,guidance,teaching", "life,death,philosophy", "daily,life,reflection", "return,repentance,rebirth",
                "teacher,grace,compassion", "practice,experience,sharing", "history,wisdom,learning", "repentance,cleansing,rebirth",
                "miracle,testimony,divine", "life,insight,wisdom", "living,moments,warmth", "soul,peace,elevation"
            ];

            private static readonly string[] EnglishContentTemplates =
            [
                "Life is like a river, and time flows like a song. In this ever-changing world, each of us is searching for our own path. Spiritual practice is not about escaping reality, but about better facing the challenges in life. When we mindfully experience every moment of life and embrace every encounter with love, we can discover extraordinary beauty in the ordinary.\n\nSpiritual growth takes time, just as flowers need sunshine and rain. On the path of practice, we will encounter various difficulties and challenges, but it is precisely these experiences that make us stronger and wiser. Every fall is for a better rise, and every loss is to find the true direction.\n\nLet us view every experience in life with a grateful heart and treat everyone around us with compassion. Only in this way can we truly appreciate the beauty and meaning of life.",

                "In this fast-paced era, we often forget to stop and listen to the voice within. Actually, wisdom lies deep in our hearts, waiting for us to discover it. The purpose of spiritual practice is not to gain something external, but to return to inner tranquility and harmony.\n\nEveryone has their own way of practice - some through meditation, some through reading, and some through serving others. No matter which way we choose, the most important thing is to have a sincere heart and continuous effort. Practice is not something that happens overnight, but a lifelong process.\n\nWhen we truly begin to practice, we will find that many problems in life become simple. Because we learn to view problems with wisdom, resolve conflicts with compassion, and accept differences with tolerance. This is the greatest gift that practice brings us.",

                "Love is the most powerful force in this world; it can dissolve all hatred and prejudice. When we look at this world with love, we will find that everyone has their own story, and everyone deserves to be understood and cared for.\n\nCompassion is not just sympathy for others, but also tolerance for ourselves. We must learn to forgive our own mistakes and accept our imperfections. Only when we truly love ourselves can we truly love others.\n\nIn daily life, we can transmit love and warmth through small actions. A smile, a greeting, a hug - these seemingly insignificant actions can bring tremendous strength to others. Let us become messengers of love and warm this world with our actions.",

                "Life is a wonderful journey, and everyone has their own unique mission and value. We come to this world not to take, but to give; not to complain, but to be grateful; not to destroy, but to build.\n\nIn the process of seeking life's meaning, we will encounter various confusions and challenges. But it is precisely these experiences that make us more mature and wise. Every setback is an opportunity for growth, and every failure is a foundation for success.\n\nTrue success is not the accumulation of wealth or the acquisition of fame, but inner peace and contentment. When we can calmly face life's ups and downs, when we can view every experience with a grateful heart, we have found the true meaning of life."
            ];

            public static List<Article> GenerateArticles(int count = 100)
            {
                var currentId = 1;
                var curId = 1; // Logical ID that matches across languages

                var faker = new Faker<Article>("en")
                    .RuleFor(a => a.Id, f => 2000 + currentId++)
                    .RuleFor(c => c.LanguageCode, f => LangCode.English)
                    .RuleFor(a => a.Title, f => f.PickRandom(EnglishArticleTitles))
                    .RuleFor(a => a.ThumbnailFileId, f => (int?)null)
                    .RuleFor(a => a.Description, f => f.PickRandom(EnglishDescriptions))
                    .RuleFor(a => a.Keywords, f => f.PickRandom(EnglishKeywords))
                    .RuleFor(a => a.Views, f => f.Random.Int(50, 10000))
                    .RuleFor(a => a.Likes, f => f.Random.Int(5, 1000))
                    .RuleFor(a => a.DeliveryDate, f => SharedDeliveryDates.GetDeliveryDate(curId++))
                    .RuleFor(a => a.ArticleContentCategory, f => f.PickRandom<ArticleContentCategory>())
                    .RuleFor(a => a.Status, f => f.PickRandom<PublishStatus>())
                    .RuleFor(a => a.Content, f => f.PickRandom(EnglishContentTemplates))
                    .RuleFor(a => a.Memo, f => f.Lorem.Sentence(3, 8))
                    .RuleFor(a => a.CreationTime, f => f.Date.Between(DateTime.Now.AddYears(-2), DateTime.Now))
                    .RuleFor(a => a.LastModificationTime, (f, a) => f.Date.Between(a.CreationTime, DateTime.Now));

                return faker.Generate(count);
            }
        }
    }
}