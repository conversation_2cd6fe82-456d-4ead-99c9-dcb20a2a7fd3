using Bogus;
using global::HolyBless.Entities.Articles;
using HolyBless.Enums;

namespace HolyBless.FakeData.DataSeeders
{
    namespace HolyBless.FakeData.DataSeeders
    {
        public static class ArticleDataHantGenerator
        {
            private static readonly string[] ChineseArticleTitles =
            [
                "心靈覺醒的旅程", "智慧之光照亮人生", "修行路上的感悟", "愛與慈悲的力量",
                "生命的真諦與意義", "夢境中的啟示", "天門開啟的奇蹟", "平安祝福的力量",
                "心靈成長的秘訣", "修行答疑解惑", "生死哲學思考", "微博生活感悟",
                "回頭是岸的故事", "師恩如山的教誨", "修行心得分享", "溫故知新的智慧",
                "虔誠懺悔的力量", "神蹟見證錄", "人生感悟隨筆", "生活智慧點滴",
                "心靈淨化之路", "修行體驗分享", "生命覺醒時刻", "愛的溫暖傳遞",
                "智慧人生指南", "心靈平靜之道", "修行困惑解答", "生活哲學思考",
                "夢想成真的力量", "神恩浩蕩見證", "師父慈悲教導", "懺悔重生之路",
                "奇蹟人生故事", "溫暖人心的愛", "心靈昇華體驗", "修行智慧分享",
                "生命意義探索", "夢境奇遇記", "天啟之路指引", "平安護佑見證",
                "心靈覺醒感悟", "修行方法指導", "人生智慧總結", "生活感言錄",
                "浪子回頭金不換", "恩師智慧傳承", "修行收穫心得", "歷史啟示錄",
                "懺悔心聲傾訴", "神蹟恩典見證", "人生思考隨筆", "生活點滴記錄"
            ];

            private static readonly string[] ChineseDescriptions =
            [
                "分享心靈成長的體驗，探索內心深處的智慧與力量。",
                "記錄修行路上的感悟與體驗，為修行者提供指引。",
                "用愛與慈悲溫暖人心，傳遞正能量與希望。",
                "探索生命的真諦，分享人生的智慧與感悟。",
                "記錄夢境中的奇遇與啟示，解析夢的深層含義。",
                "見證天門開啟的奇蹟，感受神聖的力量與護佑。",
                "分享平安祝福的力量，傳遞神恩浩蕩的見證。",
                "指導心靈成長的方法，幫助心靈淨化與昇華。",
                "解答修行路上的疑惑，為修行者指明方向。",
                "思考生死的奧秘，探索人生的哲學與智慧。",
                "記錄生活的點滴感悟，分享微博中的智慧。",
                "分享回頭是岸的故事，見證重新開始的力量。",
                "傳承師恩如山的教誨，銘記恩師的慈悲與智慧。",
                "分享修行路上的心得體會，記錄修行的點點滴滴。",
                "溫故而知新，從歷史中汲取智慧與啟示。",
                "虔誠懺悔洗滌心靈，分享懺悔的感悟與體驗。",
                "記錄神蹟顯現的見證，分享奇蹟的故事與感悟。",
                "隨筆記錄人生感悟，分享生活的智慧與思考。",
                "點滴記錄生活智慧，傳遞溫暖與正能量。",
                "指引心靈淨化之路，幫助心靈覺醒與成長。"
            ];

            private static readonly string[] ChineseKeywords =
            [
                "心靈,覺醒,智慧", "修行,感悟,體驗", "愛,慈悲,溫暖", "生命,真諦,意義",
                "夢境,啟示,奇遇", "天門,奇蹟,神聖", "平安,祝福,護佑", "心靈,成長,淨化",
                "修行,答疑,指導", "生死,哲學,智慧", "微博,生活,感悟", "回頭,悔過,重生",
                "師恩,教誨,慈悲", "修行,心得,分享", "溫故,知新,歷史", "懺悔,洗滌,重生",
                "神蹟,見證,奇蹟", "人生,感悟,智慧", "生活,點滴,溫暖", "心靈,平靜,昇華"
            ];

            private static readonly string[] ChineseContentTemplates =
            [
                "人生如河，歲月如歌。在這個充滿變化的世界裡，我們每個人都在尋找屬於自己的道路。修行不是逃避現實，而是更好地面對生活中的挑戰。當我們用心去感受生活的每一個瞬間，用愛去擁抱每一個遇見，我們就能在平凡中發現不平凡的美。\n\n心靈的成長需要時間，就像花朵需要陽光和雨露一樣。在修行的路上，我們會遇到各種困難和挑戰，但正是這些經歷讓我們變得更加堅強和智慧。每一次的跌倒都是為了更好的站起來，每一次的迷失都是為了找到真正的方向。\n\n讓我們用感恩的心去看待生活中的每一個經歷，用慈悲的心去對待身邊的每一個人。只有這樣，我們才能真正體會到生命的美好和意義。",

                "在這個快節奏的時代，我們常常忘記停下來聆聽內心的聲音。其實，智慧就在我們的內心深處，等待著我們去發現。修行的目的不是為了獲得什麼外在的東西，而是為了回歸內心的寧靜與和諧。\n\n每個人都有自己的修行方式，有些人通過冥想，有些人通過閱讀，有些人通過服務他人。無論選擇哪種方式，最重要的是要有一顆虔誠的心和持續的努力。修行不是一朝一夕的事情，而是一個終身的過程。\n\n當我們真正開始修行時，會發現生活中的很多問題都變得簡單了。因為我們學會了用智慧去看待問題，用慈悲去化解矛盾，用包容去接納差異。這就是修行給我們帶來的最大禮物。",

                "愛是這個世界上最強大的力量，它能夠化解一切的仇恨和偏見。當我們用愛去看待這個世界時，我們會發現每個人都有自己的故事，每個人都值得被理解和關愛。\n\n慈悲不僅僅是對他人的同情，更是對自己的寬容。我們要學會原諒自己的過錯，接納自己的不完美。只有當我們真正愛自己時，我們才能真正愛別人。\n\n在日常生活中，我們可以通過小小的行為來傳遞愛和溫暖。一個微笑、一句問候、一個擁抱，這些看似微不足道的行為，卻能夠給他人帶來巨大的力量。讓我們成為愛的傳遞者，用我們的行動去溫暖這個世界。",

                "生命是一段奇妙的旅程，每個人都有自己獨特的使命和價值。我們來到這個世界上，不是為了索取，而是為了給予；不是為了抱怨，而是為了感恩；不是為了破壞，而是為了建設。\n\n在尋找生命意義的過程中，我們會遇到各種各樣的困惑和挑戰。但正是這些經歷讓我們變得更加成熟和智慧。每一次的挫折都是成長的機會，每一次的失敗都是成功的鋪墊。\n\n真正的成功不是財富的積累，不是名聲的獲得，而是內心的平靜和滿足。當我們能夠坦然面對生活的起起落落，當我們能夠用感恩的心去看待每一個經歷，我們就找到了生命的真諦。"
            ];

            public static List<Article> GenerateArticles(int count = 100)
            {
                var currentId = 1;
                var curId = 1; // Logical ID that matches across languages

                var faker = new Faker<Article>("zh_TW")
                    .RuleFor(a => a.Id, f => 1000 + currentId++)
                    .RuleFor(c => c.LanguageCode, f => LangCode.TraditionalChinese)
                    .RuleFor(a => a.Title, f => f.PickRandom(ChineseArticleTitles))
                    .RuleFor(a => a.ThumbnailFileId, f => (int?)null)
                    .RuleFor(a => a.Description, f => f.PickRandom(ChineseDescriptions))
                    .RuleFor(a => a.Keywords, f => f.PickRandom(ChineseKeywords))
                    .RuleFor(a => a.Views, f => f.Random.Int(50, 10000))
                    .RuleFor(a => a.Likes, f => f.Random.Int(5, 1000))
                    .RuleFor(a => a.DeliveryDate, f => SharedDeliveryDates.GetDeliveryDate(curId++))
                    .RuleFor(a => a.ArticleContentCategory, f => f.PickRandom<ArticleContentCategory>())
                    .RuleFor(a => a.Status, f => f.PickRandom<PublishStatus>())
                    .RuleFor(a => a.Content, f => f.PickRandom(ChineseContentTemplates))
                    .RuleFor(a => a.Memo, f => f.Lorem.Sentence(3, 8))
                    .RuleFor(a => a.CreationTime, f => f.Date.Between(DateTime.Now.AddYears(-2), DateTime.Now))
                    .RuleFor(a => a.LastModificationTime, (f, a) => f.Date.Between(a.CreationTime, DateTime.Now));

                return faker.Generate(count);
            }
        }
    }
}