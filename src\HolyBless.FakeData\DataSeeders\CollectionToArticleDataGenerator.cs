using Bogus;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Collections;

namespace HolyBless.FakeData.DataSeeders
{
    /// <summary>
    /// Unified generator for CollectionToArticle relationships across all languages
    /// </summary>
    public static class CollectionToArticleDataGenerator
    {
        public static List<CollectionToArticle> GenerateCollectionToArticles(
            List<Collection> collections,
            List<Article> articles,
            int minArticlesPerCollection = 3,
            int maxArticlesPerCollection = 15)
        {
            var collectionToArticles = new List<CollectionToArticle>();
            var faker = new Faker();

            foreach (var collection in collections)
            {
                // Randomly select how many articles this collection will have
                var articleCount = faker.Random.Int(minArticlesPerCollection, maxArticlesPerCollection);

                // Randomly select articles for this collection
                var selectedArticles = faker.PickRandom(articles, articleCount).ToList();

                foreach (var article in selectedArticles)
                {
                    // Check if this combination already exists to avoid duplicates
                    if (!collectionToArticles.Any(cta => cta.CollectionId == collection.Id && cta.ArticleId == article.Id))
                    {
                        collectionToArticles.Add(new CollectionToArticle
                        {
                            CollectionId = collection.Id,
                            ArticleId = article.Id,
                            Weight = faker.Random.Int(0, 100),
                            IsDeleted = false
                        });
                    }
                }
            }

            return collectionToArticles;
        }

        /// <summary>
        /// Generate relationships using only IDs (useful when entities are not loaded)
        /// </summary>
        public static List<CollectionToArticle> GenerateCollectionToArticlesByIds(
            List<int> collectionIds,
            List<int> articleIds,
            int minArticlesPerCollection = 3,
            int maxArticlesPerCollection = 15)
        {
            var collectionToArticles = new List<CollectionToArticle>();
            var faker = new Faker();

            foreach (var collectionId in collectionIds)
            {
                // Randomly select how many articles this collection will have
                var articleCount = faker.Random.Int(minArticlesPerCollection, maxArticlesPerCollection);

                // Randomly select article IDs for this collection
                var selectedArticleIds = faker.PickRandom(articleIds, articleCount).ToList();

                foreach (var articleId in selectedArticleIds)
                {
                    // Check if this combination already exists to avoid duplicates
                    if (!collectionToArticles.Any(cta => cta.CollectionId == collectionId && cta.ArticleId == articleId))
                    {
                        collectionToArticles.Add(new CollectionToArticle
                        {
                            CollectionId = collectionId,
                            ArticleId = articleId,
                            Weight = faker.Random.Int(0, 100),
                            IsDeleted = false
                        });
                    }
                }
            }

            return collectionToArticles;
        }

        /// <summary>
        /// Generate with balanced distribution - ensures each article appears in at least one collection
        /// </summary>
        public static List<CollectionToArticle> GenerateBalancedCollectionToArticles(
            List<Collection> collections,
            List<Article> articles)
        {
            var collectionToArticles = new List<CollectionToArticle>();
            var faker = new Faker();

            // First, ensure each article appears in at least one collection
            foreach (var article in articles)
            {
                var randomCollection = faker.PickRandom(collections);
                collectionToArticles.Add(new CollectionToArticle
                {
                    CollectionId = randomCollection.Id,
                    ArticleId = article.Id,
                    Weight = faker.Random.Int(0, 100),
                    IsDeleted = false
                });
            }

            // Then, add additional relationships to make it more realistic
            var additionalRelationships = faker.Random.Int(articles.Count / 2, articles.Count * 2);

            for (int i = 0; i < additionalRelationships; i++)
            {
                var randomCollection = faker.PickRandom(collections);
                var randomArticle = faker.PickRandom(articles);

                // Check if this combination already exists to avoid duplicates
                if (!collectionToArticles.Any(cta => cta.CollectionId == randomCollection.Id && cta.ArticleId == randomArticle.Id))
                {
                    collectionToArticles.Add(new CollectionToArticle
                    {
                        CollectionId = randomCollection.Id,
                        ArticleId = randomArticle.Id,
                        Weight = faker.Random.Int(0, 100),
                        IsDeleted = false
                    });
                }
            }

            return collectionToArticles;
        }
    }
}
