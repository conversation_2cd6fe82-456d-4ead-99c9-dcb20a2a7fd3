using Bogus;

namespace HolyBless.FakeData.DataSeeders
{
    /// <summary>
    /// Provides shared delivery dates across all article generators to ensure 
    /// articles with the same curId have the same DeliveryDate regardless of language
    /// </summary>
    public static class SharedDeliveryDates
    {
        private static readonly Dictionary<int, DateTime> _deliveryDates = GenerateSharedDeliveryDates();
        
        private static Dictionary<int, DateTime> GenerateSharedDeliveryDates()
        {
            var deliveryDates = new Dictionary<int, DateTime>();
            var faker = new Faker();
            
            // Generate 100 shared delivery dates for curId 1-100
            // Using a fixed seed to ensure consistency across runs
            faker.Random = new Randomizer(12345);
            
            for (int curId = 1; curId <= 100; curId++)
            {
                deliveryDates[curId] = faker.Date.Between(DateTime.Now.AddYears(-3), DateTime.Now);
            }
            
            return deliveryDates;
        }
        
        /// <summary>
        /// Gets the delivery date for a specific curId
        /// </summary>
        /// <param name="curId">The logical ID (1-100)</param>
        /// <returns>The delivery date for this curId</returns>
        public static DateTime GetDeliveryDate(int curId)
        {
            if (!_deliveryDates.ContainsKey(curId))
            {
                throw new ArgumentException($"Invalid curId: {curId}. Must be between 1 and 100.");
            }
            
            return _deliveryDates[curId];
        }
    }
}
