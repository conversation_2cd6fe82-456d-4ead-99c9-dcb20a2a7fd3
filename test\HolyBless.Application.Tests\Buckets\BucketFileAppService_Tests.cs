using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Buckets.Dtos;
using HolyBless.Enums;
using Shouldly;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace HolyBless.Buckets
{
    public abstract class BucketFileAppService_Tests<TStartupModule> : HolyBlessApplicationTestBase<TStartupModule>
            where TStartupModule : IAbpModule
    {
        private readonly IBucketFileAppService _bucketFileAppService;

        protected BucketFileAppService_Tests()
        {
            _bucketFileAppService = GetRequiredService<IBucketFileAppService>();
        }

        [Fact]
        public async Task Should_Get_List_Of_BucketFiles()
        {
            // Act
            var result = await _bucketFileAppService.GetListAsync(new PagedAndSortedResultRequestDto());

            // Assert
            result.TotalCount.ShouldBeGreaterThan(0);
            result.Items.ShouldContain(bf => bf.FileName == "File1");

            var item = result.Items.FirstOrDefault(bf => bf.FileName == "File1");
            item.ShouldNotBeNull();
            item.MediaType.ShouldBe(MediaType.Image);

            //Action
            var singleItem = await _bucketFileAppService.GetAsync(item.Id);

            singleItem.ShouldNotBeNull();
            //singleItem.StorageBucketToFiles.ShouldContain(x => x.BucketName == "Bucket1");
        }

        [Fact]
        public async Task Should_Create_A_New_BucketFile()
        {
            // Act
            var result = await _bucketFileAppService.CreateAsync(
                new CreateUpdateBucketFileDto
                {
                    FileName = "NewFile",
                    Title = "New File Title",
                    RelativePathInBucket = "/new/file/path",
                    LanguageCode = "EN",
                    ContentCategory = ContentCategory.Thumbnail,
                    YoutubeId = "new-youtube-id",
                    Environment = "Test",
                }
            );

            // Assert
            result.Id.ShouldNotBe(0);
            result.FileName.ShouldBe("NewFile");
            result.Environment.ShouldBe("Test");
            result.Exists.ShouldBe(true);
            result.ComputeUrl.ShouldBe("https://example.com/newfile");
        }

        [Fact]
        public async Task Should_Not_Create_A_BucketFile_Without_FileName()
        {
            // Act & Assert
            await Assert.ThrowsAsync<AbpValidationException>(async () =>
            {
                await _bucketFileAppService.CreateAsync(
                    new CreateUpdateBucketFileDto
                    {
                        Title = "File without name",
                        RelativePathInBucket = "/new/file/path",
                        LanguageCode = "EN",
                        ContentCategory = ContentCategory.Thumbnail,
                        YoutubeId = "new-youtube-id",
                        Environment = "Test",
                    }
                );
            });
        }

        [Fact]
        public async Task Should_Update_Existing_BucketFile()
        {
            // Arrange
            var bucketFile = await _bucketFileAppService.CreateAsync(
                new CreateUpdateBucketFileDto
                {
                    FileName = "UpdateFile",
                    Title = "File to be updated",
                    RelativePathInBucket = "/update/file/path",
                    LanguageCode = "EN",
                    ContentCategory = ContentCategory.OriginalVideo,
                    YoutubeId = "update-youtube-id",
                    Environment = "Dev",
                }
            );

            // Act
            var result = await _bucketFileAppService.UpdateAsync(
                bucketFile.Id,
                new CreateUpdateBucketFileDto
                {
                    FileName = "UpdatedFile",
                    Title = "Updated File Title",
                    RelativePathInBucket = "/updated/file/path",
                    LanguageCode = "EN",
                    ContentCategory = ContentCategory.OriginalVideo,
                    YoutubeId = "updated-youtube-id",
                    Environment = "Prod",
                }
            );

            // Assert
            result.FileName.ShouldBe("UpdatedFile");
            result.Environment.ShouldBe("Prod");
            result.Exists.ShouldBe(false);
            result.ComputeUrl.ShouldBe("https://example.com/updatedfile");
        }

        [Fact]
        public async Task Should_Delete_Existing_BucketFile()
        {
            // Arrange
            var bucketFile = await _bucketFileAppService.CreateAsync(
                new CreateUpdateBucketFileDto
                {
                    FileName = "DeleteFile",
                    Title = "File to be deleted",
                    RelativePathInBucket = "/delete/file/path",
                    LanguageCode = "EN",
                    ContentCategory = ContentCategory.Thumbnail,
                    YoutubeId = "delete-youtube-id",
                    Environment = "Test",
                }
            );

            // Act
            await _bucketFileAppService.DeleteAsync(bucketFile.Id);

            // Assert
            var result = await _bucketFileAppService.GetListAsync(new PagedAndSortedResultRequestDto());
            result.Items.ShouldNotContain(bf => bf.FileName == "DeleteFile");
        }
    }
}