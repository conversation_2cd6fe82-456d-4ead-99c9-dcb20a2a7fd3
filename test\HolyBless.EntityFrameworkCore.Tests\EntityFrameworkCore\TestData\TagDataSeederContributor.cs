using System.Threading.Tasks;
using HolyBless.Entities.Tags;
using HolyBless.Enums;
using HolyBless.Lookups;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.DataSeeders
{
    public class TagDataSeederContributor : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<Tag, int> _tagRepository;

        public TagDataSeederContributor(
            IRepository<Tag, int> tagRepository)
        {
            _tagRepository = tagRepository;
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            if (await _tagRepository.GetCountAsync() > 0)
            {
                return;
            }

            // Create the tags
            await _tagRepository.InsertAsync(
                new Tag
                {
                    ContentCode = "TechonologyCode",
                    TagName = "Technology",
                    LanguageCode = "en",
                    Views = 100
                },
                autoSave: true
            );

            await _tagRepository.InsertAsync(
                new Tag
                {
                    ContentCode = "HealthCode",
                    TagName = "Health",
                    LanguageCode = "en",
                    Views = 75
                },
                autoSave: true
            );

            await _tagRepository.InsertAsync(
                new Tag
                {
                    ContentCode = "ScienceCode",
                    TagName = "Science",
                    LanguageCode = "en",
                    Views = 80
                },
                autoSave: true
            );

            await _tagRepository.InsertAsync(
                new Tag
                {
                    ContentCode = "ArtCode",
                    TagName = "Art",
                    LanguageCode = "en",
                    Views = 50
                },
                autoSave: true
            );

            await _tagRepository.InsertAsync(
                new Tag
                {
                    ContentCode = "FinanceCode",
                    TagName = "Finance",
                    LanguageCode = "en",
                    Views = 65
                },
                autoSave: true
            );
        }
    }
}