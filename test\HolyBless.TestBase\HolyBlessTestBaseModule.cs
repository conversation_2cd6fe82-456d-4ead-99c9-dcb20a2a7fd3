﻿using System.Linq;
using System;
using HolyBless.Configs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Authorization;
using Volo.Abp.Autofac;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Data;
using Volo.Abp.Modularity;
using Volo.Abp.Threading;
using HolyBless.SeedData;
using HolyBless.DataSeeders;

namespace HolyBless;

[DependsOn(
    typeof(AbpAutofacModule),
    typeof(AbpTestBaseModule),
    typeof(AbpAuthorizationModule),
    typeof(AbpBackgroundJobsAbstractionsModule)
)]
public class HolyBlessTestBaseModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpBackgroundJobOptions>(options =>
        {
            options.IsJobExecutionEnabled = false;
        });

        context.Services.AddAlwaysAllowAuthorization();
        var configuration = context.Services.GetConfiguration();
        //This support DI: IOption<AppConfig>
        //context.Services.Configure<AppConfig>(configuration.GetSection("AppConfigs"));
        // Register AppConfig
        var env = Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Development";

        context.Services.AddSingleton(sp =>
        {
            var config = sp.GetRequiredService<IConfiguration>();
            var appSetting = (config.GetSection("AppConfigs").Get<AppConfig>()) ?? new();
            appSetting.Environment = env;
            return appSetting;
        });
        ConfigureDataSeederOrders();
    }

    public void ConfigureDataSeederOrders()
    {
        Configure<AbpDataSeedOptions>(options =>
        {
            // This is automatic, but we do it here because the order matters.
            var requestedOrder = new Type[] {
                typeof(TestChannelDataSeeder),
                typeof(TestStorageProviderDataSeeder),typeof(TestStorageProviderDataSeeder),
                typeof(TestBucketFileDataSeeder), typeof(TestCollectionDataSeeder),
                typeof(TestTagDataSeeder), typeof(TestArticleDataSeeder) };

            var orderedContributors = options.Contributors
                .OrderBy(contributor =>
                {
                    var index = Array.IndexOf(requestedOrder, contributor);
                    return index == -1 ? int.MaxValue : index;
                })
                .ToList();

            options.Contributors.Clear();
            foreach (var contributor in orderedContributors)
            {
                options.Contributors.Add(contributor);
            }
        });
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        SeedTestData(context);
    }

    private static void SeedTestData(ApplicationInitializationContext context)
    {
        AsyncHelper.RunSync(async () =>
        {
            using (var scope = context.ServiceProvider.CreateScope())
            {
                await scope.ServiceProvider
                    .GetRequiredService<IDataSeeder>()
                    .SeedAsync();
            }
        });
    }
}