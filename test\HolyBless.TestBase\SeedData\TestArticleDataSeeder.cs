using System;
using System.Threading.Tasks;
using HolyBless.Entities.Articles;
using HolyBless.Enums;
using HolyBless.Lookups;
using Microsoft.Extensions.Logging;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.SeedData
{
    public class TestArticleDataSeeder : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<Article, int> _articleRepository;
        private readonly ILogger<TestArticleDataSeeder> _logger;

        public TestArticleDataSeeder(
            IRepository<Article, int> articleRepository,
            ILogger<TestArticleDataSeeder> logger)
        {
            _articleRepository = articleRepository;
            _logger = logger;
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            if (await _articleRepository.GetCountAsync() > 0)
            {
                _logger.LogInformation("Articles already seeded. Skipping...");
                return;
            }

            _logger.LogInformation("Seeding test articles...");

            await _articleRepository.InsertAsync(
                new Article
                {
                    Title = "Introduction to ABP Framework",
                    LanguageCode = "en",
                    Description = "A comprehensive introduction to the ABP Framework and its features",
                    Keywords = "abp, framework, introduction, dotnet",
                    Status = PublishStatus.Published,
                    ArticleContentCategory = ArticleContentCategory.Article,
                    Content = "The ABP Framework is a complete infrastructure to create modern web applications by following the best practices and conventions of software development."
                },
                autoSave: true
            );

            await _articleRepository.InsertAsync(
                new Article
                {
                    Title = "Entity Framework Core Basics",
                    LanguageCode = "en",
                    Description = "Understanding Entity Framework Core concepts",
                    Keywords = "entity framework, ef core, orm, database",
                    Status = PublishStatus.Published,
                    ArticleContentCategory = ArticleContentCategory.Lecture,
                    Content = "Entity Framework Core is a lightweight, extensible, open source and cross-platform version of the popular Entity Framework data access technology."
                },
                autoSave: true
            );

            await _articleRepository.InsertAsync(
                new Article
                {
                    Title = "Using ABP CLI",
                    LanguageCode = "en",
                    Description = "Guide to using the ABP Command Line Interface",
                    Keywords = "abp, cli, command line, tools",
                    Status = PublishStatus.Draft,
                    ArticleContentCategory = ArticleContentCategory.Article,
                    Content = "ABP CLI (Command Line Interface) is a command line tool to perform some common operations for ABP based solutions."
                },
                autoSave: true
            );

            _logger.LogInformation("Seeded test articles successfully.");
        }
    }
}